#!/usr/bin/env python3
"""
Test script for aiXplain API integration (Anymodel support removed)
"""

import asyncio
import json
import httpx
from run_token import call_aixplain_api

async def test_aixplain_direct():
    """Test aiXplain API directly"""
    print("Testing aiXplain API directly...")
    
    try:
        result = await call_aixplain_api(
            model_id="test-model",  # Replace with actual model ID
            text="你是谁？",
            max_tokens=64000,
            temperature=0.0,
            top_p=1.0,
            context="",
            history=[],
            timeout=300
        )
        
        print("Success!")
        print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

async def test_openai_format():
    """Test the OpenAI-compatible endpoint"""
    print("\nTesting OpenAI-compatible endpoint...")
    
    # You'll need to replace this with your actual client API key
    client_api_key = "sk-dummy-test"  # Replace with actual key from client_api_keys.json
    
    payload = {
        "model": "test-model",  # Replace with actual model ID
        "messages": [
            {"role": "user", "content": "你是谁？"}
        ],
        "stream": False,
        "temperature": 0.0,
        "max_tokens": 64000,
        "top_p": 1.0
    }
    
    headers = {
        "Authorization": f"Bearer {client_api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "http://localhost:8000/v1/chat/completions",
                json=payload,
                headers=headers
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("aiXplain API Test Script")
    print("=" * 40)
    
    # Test direct API call
    asyncio.run(test_aixplain_direct())
    
    # Test OpenAI format (requires server to be running)
    print("\nTo test OpenAI format:")
    print("1. Start the server: python run_token.py")
    print("2. Update the model_id and client_api_key in this script")
    print("3. Uncomment the line below")
    # asyncio.run(test_openai_format())
