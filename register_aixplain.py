import asyncio
import json
import random
import string
import re
import time
from mailtmapi import MailTM
import aiohttp

# --- Configuration ---
URL_CHECK_EMAIL = "https://auth-api.aixplain.com/register/check-email"
URL_REGISTER = "https://auth-api.aixplain.com/register"
URL_PRE_AUTH = "https://auth-api.aixplain.com/auth"
URL_CONFIRM = "https://auth-api.aixplain.com/register/confirmation"
URL_AUTH = "https://auth-api.aixplain.com/auth"
URL_GRAPHQL = "https://platform-api.aixplain.com/graphql"
URL_AUTH_APP = "https://auth-api.aixplain.com/auth/app"
PASSWORD_LENGTH = 12
EMAIL_FETCH_ATTEMPTS = 5  # Number of attempts to fetch the email
EMAIL_FETCH_DELAY = 10     # Seconds to wait between attempts
DELAY_BETWEEN_RUNS_SECONDS = 50
MAX_REGISTRATION_ATTEMPTS = 100  # Maximum number of registration attempts
MIN_DELAY_SECONDS = 30  # Minimum delay between attempts
MAX_DELAY_SECONDS = 120  # Maximum delay between attempts
RECAPTCHA_TOKEN = "03AFcWeA6lQ33zYxhl_VQHdA8leP8d2TB9Y1XBhwDEuiZ5COg8DKu6Cyd8pqiBLaQwDA9160mC36eJD4GnJCgoNYdmk17YHRB4gl5J48DMsM6kwi54ZXv4MObGb5Fq50fPUMy36xg19XW4mJNW1ualk-UHKM4KH0l2B4wIzV9-0rW6tFnSAqFggyB3DANtPHSxRUQBbEeybq8xq1SK5ktb2H8guwGdLlO1h8ZB1XWs-f6J5vRnAJXdqFYBoIs5yDKe6ccvtLpY56TExuJlMtC6KtT_uRJMF3vZpWunh_OejdnhFYxkMIEkkO2HkDi-Iz0fbxrzd_CFV1cy4A35B6NJ095yIgBBhH4KVlqoe5XirwVPVWVcNfWCXS609KrJz7GTZxZkDumm9Tzii4MIrTyDtgB9ztK2BjEjGf72eujQoo4KoK5YiQHQIgKolk-ZsycjYZf9HiriEnBdzIvXtmSBPreEslyPJBaNTX_d7s9eptGhgxwHgA9f86wX9UmziBrU17hHrEel9AV9lYb2kBApJd73vs1oLNd6qjpTlMZAZ0BGsfMPi89-iO42KXt4PlVNNmGXjvAsVNqy2rZYcPi7zBi7DNNsby0t1gDhEJuopHvTu4x-XdJJxbICqp8TeGbWP9Xgnf-oLuhYBcHJ5d7oJcuPnADTCy6PnAI-3n-IriAWIC8fXpve8ODfp90VFzPDx3N7XA57hU3qSwjJM3nMzEn9azh7jf0rLCw2VZBxjUpU6d1QFQbrEBpwx_cO661mcLO2QHPQuUCADyxNUYzxU7HWlxTerh_UgfGogMSj0Y7b6TX1W4tlGvBprgz6r_Hu9HpWA5knkIr2SoCA4qRkmhlfUOqDaCuYzf81iXEWnJkZcAtX9FF_sMeJ3CHF9HjaM4ySsgfNwrYPxaUfZLVA7BCxkh8NxWH6AA"

# --- Helper Functions ---
def get_random_user_agent():
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    ]
    return random.choice(user_agents)

def generate_random_password(length: int) -> str:
    """Generates a random password with at least one uppercase, one lowercase, one digit, and one special character."""
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"
    
    # Ensure at least one of each character type
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special)
    ]
    
    # Fill the rest with random characters
    all_chars = lowercase + uppercase + digits + special
    password.extend(random.choice(all_chars) for _ in range(length - 4))
    
    # Shuffle the password
    random.shuffle(password)
    password = ''.join(password)
    
    print(f"🔑 Generated random password: {password}")
    return password

def generate_random_name() -> tuple:
    """Generates a random first and last name."""
    first_names = ["Alex", "Jamie", "Jordan", "Taylor", "Casey", "Riley", "Morgan", "Avery", 
                  "Quinn", "Skyler", "Blake", "Reese", "Dakota", "Hayden", "Parker", "Cameron"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Miller", "Davis", 
                 "Garcia", "Rodriguez", "Wilson", "Martinez", "Anderson", "Taylor", "Thomas"]
    
    first_name = random.choice(first_names)
    last_name = random.choice(last_names)
    
    print(f"👤 Generated random name: {first_name} {last_name}")
    return first_name, last_name

def extract_verification_code(email_text: str) -> str | None:
    """Extracts a 6-digit verification code from email text using regex."""
    match = re.search(r"\b(\d{6})\b", email_text)
    if match:
        code = match.group(1)
        print(f"📧 Found verification code in email: {code}")
        return code
    else:
        # Fallback: try to find any 6 digit number if the text is short
        if len(email_text) < 100:
            match = re.search(r"(\d{6})", email_text)
            if match:
                code = match.group(1)
                print(f"📧 Found potential verification code (fallback): {code}")
                return code
        print(f"⚠️ Could not find 6-digit verification code in text: {email_text[:200]}...")
        return None

def save_token_to_file(token, email, password):
    """保存token到文件"""
    with open("aixplain_tokens.txt", "a+") as f:
        f.write(f"{token}----{email}----{password}\n")



def generate_random_key_name() -> str:
    """生成随机的API key名称"""
    return ''.join(random.choice(string.digits) for _ in range(6))

async def register_single_account():
    mailtm = MailTM()
    temp_account = None
    account_token_data = None
    verification_code = None
    random_pw = generate_random_password(PASSWORD_LENGTH)
    first_name, last_name = generate_random_name()
    
    common_headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Origin': 'https://auth.aixplain.com',
        'Sec-GPC': '1',
        'Referer': 'https://auth.aixplain.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'TE': 'trailers'
    }

    async with aiohttp.ClientSession() as session:
        try:
            # 1. Get temporary email account
            print("⏳ 1. Creating temporary email account...")
            temp_account = await mailtm.get_account(password=random_pw)
            if not temp_account or not hasattr(temp_account, 'address'):
                print("❌ Failed to create a temporary email account or account object is malformed.")
                return
            print(f"📬 Temporary Email Account: {temp_account.address}")
            
            # 2. Check email availability
            print(f"\n⏳ 2. Checking email availability for {temp_account.address}...")
            data_check_email = {"email": temp_account.address}
            async with session.post(URL_CHECK_EMAIL, headers=common_headers, json=data_check_email) as response:
                print(f"📨 Check email response status: {response.status}")
                if response.status not in [200, 201, 204]:
                    print(f"❌ Failed to check email availability. Status: {response.status}")
                    return

            # 3. Register account
            print(f"\n⏳ 3. Registering account for {temp_account.address}...")
            register_headers = common_headers.copy()
            register_headers['recaptcha'] = RECAPTCHA_TOKEN
            
            data_register = {
                "email": temp_account.address,
                "firstName": first_name,
                "lastName": last_name,
                "password": random_pw,
                "passwordConfirmation": random_pw
            }
            
            async with session.post(URL_REGISTER, headers=register_headers, json=data_register) as response:
                print(f"📨 Register response status: {response.status}")
                response_json = await response.json()
                print(f"📨 Register response body: {json.dumps(response_json, indent=2)}")
                if response.status not in [200, 201, 204]:
                    print(f"❌ Failed to register account. Status: {response.status}")
                    return
            
            # 4. Pre-auth check
            print(f"\n⏳ 4. Performing pre-auth check for {temp_account.address}...")
            data_pre_auth = {
                "email": temp_account.address,
                "password": random_pw
            }
            
            async with session.post(URL_PRE_AUTH, headers=common_headers, json=data_pre_auth) as response:
                print(f"📨 Pre-auth response status: {response.status}")
                response_json = await response.json()
                print(f"📨 Pre-auth response body: {json.dumps(response_json, indent=2)}")
            
            # 5. Get account token for MailTM
            try:
                account_token_data = await mailtm.get_account_token(address=temp_account.address, password=random_pw)
                if not account_token_data or not hasattr(account_token_data, 'token'):
                    print(f"❌ Failed to get account token for {temp_account.address}")
                    return
                print(f"🔑 Obtained MailTM token for {temp_account.address}")
                auth_token = account_token_data.token
            except Exception as e:
                print(f"❌ Error getting MailTM account token: {e}")
                return
            
            # 6. Retrieve verification code from email
            print("\n⏳ 5. Attempting to retrieve verification code from email...")
            print(f"   (Will try {EMAIL_FETCH_ATTEMPTS} times with {EMAIL_FETCH_DELAY}s delay between attempts)")
            
            for attempt in range(EMAIL_FETCH_ATTEMPTS):
                print(f"   Attempt {attempt + 1}/{EMAIL_FETCH_ATTEMPTS} to fetch email...")
                await asyncio.sleep(EMAIL_FETCH_DELAY)
                try:
                    messages_response = await mailtm.get_messages(token=auth_token, page=1)
                    
                    if messages_response and hasattr(messages_response, 'hydra_member'):
                        messages = messages_response.hydra_member
                        if not messages:
                            print("   No messages found yet.")
                            continue

                        # Process messages
                        for message_summary in messages:
                            print(f"   Found message: ID {message_summary.id}, Subject: {message_summary.subject}")
                            # message_detail = await mailtm.get_message_by_id(token=auth_token, message_id=message_summary.id)
                            message_intro = message_summary.intro
                            
                            # if message_detail and hasattr(message_detail, 'text'):
                            #     verification_code = extract_verification_code(message_detail.text)
                            #     if verification_code:
                            #         break
                            verification_code = extract_verification_code(message_intro)
                        if verification_code:
                            break
                    else:
                        print(f"   No messages found or unexpected response structure: {messages_response}")

                except Exception as e:
                    print(f"❌ Error fetching emails (Attempt {attempt + 1}): {e}")
                    if "Unauthorized" in str(e) or "Forbidden" in str(e):
                        print("   MailTM token might be invalid/expired. Stopping email fetch.")
                        break

            if not verification_code:
                print("❌ Failed to retrieve verification code after multiple attempts.")
                return
            
            # 7. Confirm email
            print(f"\n⏳ 6. Confirming email with code: {verification_code}...")
            confirm_headers = common_headers.copy()
            confirm_headers['recaptcha'] = RECAPTCHA_TOKEN
            confirm_headers['Priority'] = 'u=0'
            
            data_confirm = {
                "code": verification_code,
                "email": temp_account.address
            }
            
            async with session.post(URL_CONFIRM, headers=confirm_headers, json=data_confirm) as response:
                print(f"📨 Confirm email response status: {response.status}")
                if response.status not in [200, 201, 204]:
                    print(f"❌ Failed to confirm email. Status: {response.status}")
                    return
            
            # 8. Login
            print(f"\n⏳ 7. Logging in with {temp_account.address}...")
            data_login = {
                "token": "",
                "email": temp_account.address,
                "password": random_pw
            }
            
            async with session.post(URL_AUTH, headers=common_headers, json=data_login) as response:
                print(f"📨 Login response status: {response.status}")
                login_response = await response.json()
                print(f"📨 Login response body: {json.dumps(login_response, indent=2)}")
                
                if response.status not in [200, 201, 204] or 'accessToken' not in login_response:
                    print(f"❌ Failed to login. Status: {response.status}")
                    return
                
                access_token = login_response['accessToken']
                print(f"🔑 Obtained access token: {access_token[:20]}...")
            
            # 9. Get team id
            print("\n⏳ 8. Getting team id...")
            graphql_headers = {
                'User-Agent': get_random_user_agent(),
                'Content-Type': 'application/json',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorization': f'Bearer {access_token}',
                'cache-control': 'no-cache',
                'origin': 'https://auth.aixplain.com',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://auth.aixplain.com/',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'x-team': 'null'
            }

            team_query = {
                "operationName": "Account",
                "variables": {},
                "query": "query Account {\n  user {\n    belEspritOnboardedAt\n    email\n    firstName\n    id\n    lastName\n    memberships {\n      teamId\n      role\n      __typename\n    }\n    ownedTeamsIds\n    phoneNumber\n    phoneNumberVerifiedAt\n    phoneNumberVerifiedBefore\n    team {\n      id\n      __typename\n    }\n    teams {\n      displayName\n      id\n      isPersonal\n      maxAccessKeysCount\n      name\n      __typename\n    }\n    __typename\n  }\n}"
            }

            async with session.post(URL_GRAPHQL, headers=graphql_headers, json=team_query) as response:
                print(f"📨 Get team id response status: {response.status}")
                team_response = await response.json()
                print(f"📨 Get team id response body: {json.dumps(team_response, indent=2)}")

                if response.status not in [200, 201, 204] or 'data' not in team_response:
                    print(f"❌ Failed to get team id. Status: {response.status}")
                    return

                team_id = team_response['data']['user']['team']['id']
                print(f"🏢 Obtained team id: {team_id}")

            # 10. Get client token
            print("\n⏳ 9. Getting client token...")
            client_headers = {
                'User-Agent': get_random_user_agent(),
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json',
                'accept-language': 'zh-CN,zh;q=0.9',
                'authorization': f'Bearer {access_token}',
                'cache-control': 'no-cache',
                'origin': 'https://auth.aixplain.com',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://auth.aixplain.com/',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site'
            }

            client_data = {"app": "platform"}

            async with session.post(URL_AUTH_APP, headers=client_headers, json=client_data) as response:
                print(f"📨 Get client token response status: {response.status}")
                client_response = await response.json()
                print(f"� Get client token response body: {json.dumps(client_response, indent=2)}")

                if response.status not in [200, 201, 204] or 'accessToken' not in client_response:
                    print(f"❌ Failed to get client token. Status: {response.status}")
                    return

                client_token = client_response['accessToken']
                print(f"🔑 Obtained client token: {client_token[:20]}...")

            # 11. Create API key
            print("\n⏳ 10. Creating API key...")
            key_name = generate_random_key_name()
            api_headers = {
                'User-Agent': get_random_user_agent(),
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Content-Type': 'application/json',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Referer': 'https://platform.aixplain.com/',
                'authorization': f'Bearer {client_token}',
                'x-team': str(team_id),
                'Origin': 'https://platform.aixplain.com',
                'Sec-GPC': '1',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
                'Priority': 'u=0',
                'TE': 'trailers'
            }

            api_query = {
                "operationName": "CreateAccessKey",
                "variables": {"name": key_name},
                "query": "mutation CreateAccessKey($expirationDate: DateTime, $isAdmin: Boolean, $name: String!) {\n  createApiTokenV2(expiresAt: $expirationDate, isAdmin: $isAdmin, name: $name) {\n    ...AccessKey\n    __typename\n  }\n}\n\nfragment AccessKey on MaskedTeamKey {\n  accessKey\n  createdAt\n  expiresAt\n  id\n  isAdmin\n  name\n  userData {\n    firstName\n    id\n    __typename\n  }\n  __typename\n}"
            }

            async with session.post(URL_GRAPHQL, headers=api_headers, json=api_query) as response:
                print(f"📨 Create API key response status: {response.status}")
                api_response = await response.json()
                print(f"📨 Create API key response body: {json.dumps(api_response, indent=2)}")

                if response.status not in [200, 201, 204] or 'data' not in api_response:
                    print(f"❌ Failed to create API key. Status: {response.status}")
                    return

                api_key = api_response['data']['createApiTokenV2']['accessKey']
                print(f"🎉 Successfully created API key: {api_key}")

                # Save to file
                save_token_to_file(api_key, temp_account.address, random_pw)
                print(f"💾 Saved API key to aixplain_tokens.txt")

        except Exception as e:
            print(f"❌ An error occurred: {e}")
            import traceback
            traceback.print_exc()

        finally:
            # Cleanup
            if account_token_data and hasattr(account_token_data, 'token') and temp_account and hasattr(temp_account, 'id'):
                try:
                    await mailtm.delete_account(token=account_token_data.token, account_id=temp_account.id)
                    print(f"🗑️ Successfully deleted MailTM account {temp_account.address}")
                except Exception as e:
                    print(f"⚠️ Could not delete MailTM account {temp_account.address}: {e}")
            elif temp_account:
                print(f"\nℹ️ MailTM account {temp_account.address} was created but could not be automatically deleted (missing ID or token).")

async def main():
    """主函数：循环执行注册，最多100次"""
    successful_registrations = 0
    failed_registrations = 0

    print(f"🚀 Starting aiXplain registration bot")
    print(f"📊 Will attempt up to {MAX_REGISTRATION_ATTEMPTS} registrations")
    print(f"⏱️ Random delay between attempts: {MIN_DELAY_SECONDS}-{MAX_DELAY_SECONDS} seconds")
    print("=" * 60)

    for attempt in range(1, MAX_REGISTRATION_ATTEMPTS + 1):
        print(f"\n🔄 === Attempt {attempt}/{MAX_REGISTRATION_ATTEMPTS} ===")
        start_time = time.time()

        try:
            # 执行单次注册
            await register_single_account()
            successful_registrations += 1
            print(f"✅ Registration {attempt} completed successfully!")

        except Exception as e:
            failed_registrations += 1
            print(f"❌ Registration {attempt} failed: {e}")
            import traceback
            traceback.print_exc()

        # 显示统计信息
        elapsed_time = time.time() - start_time
        print(f"📈 Statistics: {successful_registrations} successful, {failed_registrations} failed")
        print(f"⏱️ This attempt took {elapsed_time:.1f} seconds")

        # 如果不是最后一次尝试，等待随机时间
        if attempt < MAX_REGISTRATION_ATTEMPTS:
            delay_seconds = random.randint(MIN_DELAY_SECONDS, MAX_DELAY_SECONDS)
            print(f"⏳ Waiting {delay_seconds} seconds before next attempt...")
            await asyncio.sleep(delay_seconds)

    # 最终统计
    print("\n" + "=" * 60)
    print(f"🏁 Registration bot completed!")
    print(f"📊 Final Statistics:")
    print(f"   ✅ Successful registrations: {successful_registrations}")
    print(f"   ❌ Failed registrations: {failed_registrations}")
    print(f"   📈 Success rate: {(successful_registrations / MAX_REGISTRATION_ATTEMPTS * 100):.1f}%")

    if successful_registrations > 0:
        print(f"💾 Check 'aixplain_tokens.txt' for your API keys!")

if __name__ == '__main__':
    asyncio.run(main())