import asyncio
import json
import os
import time
import uuid
from typing import Any, Dict, List, Optional

import httpx
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

# Configuration
AIXPLAIN_API_BASE_URL = "https://platform.aixplain.com"
AIXPLAIN_API_KEY = "984fbf3fbc267c0640d96733acd60fc7ac350b8b5c703a8bde429f01194edd25"

# Global variables for client API keys
VALID_CLIENT_KEYS: set = set()
models_data: Dict[str, List[Any]] = {"data": []}

# Pydantic Models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = False
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str

class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelInfo]

class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0
    finish_reason: str = "stop"

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int] = Field(default_factory=lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})

class StreamChoice(BaseModel):
    delta: Dict[str, Any] = Field(default_factory=dict)
    index: int = 0
    finish_reason: Optional[str] = None

class StreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[StreamChoice]

# FastAPI App
app = FastAPI(title="aiXplain OpenAI API Adapter")
security = HTTPBearer(auto_error=False)



def load_models() -> Dict[str, List[ModelInfo]]:
    """Load models from models.json, filtering for 'base' tier text models"""
    global models_data
    filtered_model_list: List[ModelInfo] = []
    try:
        with open("models.json", "r", encoding="utf-8") as f:
            raw_models_data = json.load(f)
        
        text_model_list = raw_models_data.get("textModelList", [])
        
        for provider_group in text_model_list:
            provider_name = provider_group.get("name", "unknown_provider")
            for model_entry in provider_group.get("models", []):
                # Check tier, availability, and ensure it's not marked as unavailable explicitly
                is_unavailable_flag = model_entry.get("unavailable")
                is_hidden = isinstance(is_unavailable_flag, str) and is_unavailable_flag.upper() == "HIDDEN"
                
                if model_entry.get("tier") == "base" and not is_unavailable_flag and not is_hidden:
                    model_id = model_entry.get("name")
                    if model_id:
                        # 'created' field is not reliably available or in correct format in source models.json
                        # Using current time as a fallback.
                        created_timestamp = int(time.time())
                        # owned_by is the provider group name
                        filtered_model_list.append(
                            ModelInfo(
                                id=model_id,
                                created=created_timestamp,
                                owned_by=provider_name
                            )
                        )
        
        models_data = {"data": filtered_model_list}
        print(f"Successfully loaded {len(filtered_model_list)} base-tier, available text models from models.json")
            
    except FileNotFoundError:
        print("Error: models.json not found. No models will be available.")
        models_data = {"data": []}
    except json.JSONDecodeError as e:
        print(f"Error decoding models.json: {e}. No models will be available.")
        models_data = {"data": []}
    except Exception as e:
        print(f"An unexpected error occurred loading models.json: {e}")
        models_data = {"data": []}
    return models_data

def load_client_api_keys():
    """Load client API keys from client_api_keys.json"""
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            if not isinstance(keys, list):
                print("Warning: client_api_keys.json should contain a list of keys.")
                VALID_CLIENT_KEYS = set()
                return
            VALID_CLIENT_KEYS = set(keys)
            print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found. Client authentication will fail.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()



def get_model_item(model_id: str) -> Optional[ModelInfo]:
    """Get model item by ID from loaded models data"""
    for model_info_obj in models_data.get("data", []):
        if model_info_obj.id == model_id:
            return model_info_obj
    return None

async def authenticate_client(auth: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        print("Critical: No client API keys configured. Denying all requests.")
        raise HTTPException(status_code=503, detail="Service unavailable: Client API keys not configured on server.")
    
    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")



async def call_aixplain_api(model_id: str, text: str, max_tokens: int = 64000,
                           temperature: float = 0.0, top_p: float = 1.0,
                           context: str = "", history: list = None, timeout: int = 300) -> dict:
    """Call aiXplain API with two-step process"""
    if history is None:
        history = []

    # Step 1: Send POST request to execute endpoint
    execute_url = f"{AIXPLAIN_API_BASE_URL}/api/v1/execute/{model_id}"
    headers = {
        "x-api-key": AIXPLAIN_API_KEY,
        "Content-Type": "application/json"
    }

    payload = {
        "text": text,
        "max_tokens": max_tokens,
        "context": context,
        "history": history,
        "temperature": temperature,
        "top_p": top_p,
        "timeout": timeout
    }

    async with httpx.AsyncClient(timeout=timeout + 30) as client:
        # Step 1: Execute request
        response = await client.post(execute_url, json=payload, headers=headers)
        response.raise_for_status()

        execute_result = response.json()
        data_url = execute_result.get("data")

        if not data_url:
            raise HTTPException(status_code=502, detail="aiXplain API did not return data URL")

        # Step 2: Poll the data URL until completion
        max_poll_attempts = 60  # Poll for up to 60 attempts (5 minutes with 5s intervals)
        poll_interval = 5  # seconds

        for attempt in range(max_poll_attempts):
            try:
                data_response = await client.get(data_url)
                data_response.raise_for_status()

                result = data_response.json()

                # Check if completed
                if result.get("completed", False) and result.get("status") == "SUCCESS":
                    return result
                elif result.get("status") == "FAILED":
                    raise HTTPException(status_code=502, detail=f"aiXplain API failed: {result.get('error', 'Unknown error')}")

                # Wait before next poll
                await asyncio.sleep(poll_interval)

            except httpx.HTTPStatusError as e:
                if attempt == max_poll_attempts - 1:  # Last attempt
                    raise HTTPException(status_code=502, detail=f"Failed to get result from aiXplain: {e}")
                await asyncio.sleep(poll_interval)

        # If we reach here, polling timed out
        raise HTTPException(status_code=504, detail="aiXplain API polling timeout")

@app.on_event("startup")
async def startup():
    """应用启动时初始化配置"""
    print("Starting aiXplain OpenAI API Adapter server...")
    load_models()
    load_client_api_keys()
    print("Server initialization completed.")

@app.get("/v1/models", response_model=ModelList)
async def list_v1_models(_: None = Depends(authenticate_client)):
    """List available models - authenticated"""
    return await get_models_list_response()

@app.get("/models", response_model=ModelList)
async def list_models_no_auth():
    """List available models without authentication - for client compatibility"""
    return await get_models_list_response()

async def get_models_list_response() -> ModelList:
    """Helper to construct ModelList response"""
    loaded_model_info_objects = models_data.get("data", [])
    
    if not loaded_model_info_objects:
        print("Warning: No base-tier models loaded or available from models.json, using fallback default models for /models endpoint.")
        # Fallback default models are more for ensuring the endpoint doesn't crash than for actual use.
        fallback_data = [
            ModelInfo(id="anthropic/claude-3-5-sonnet-20240620", created=int(time.time()), owned_by="anthropic"),
            ModelInfo(id="openai/gpt-4o-mini", created=int(time.time()), owned_by="openai")
        ]
        return ModelList(data=fallback_data)
    
    return ModelList(data=loaded_model_info_objects)

@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    _: None = Depends(authenticate_client)
):
    """Create chat completion using aiXplain backend"""
    if not get_model_item(request.model):
        raise HTTPException(status_code=404, detail=f"Model '{request.model}' not found or not available. Ensure it is a configured base-tier model.")

    # Use aiXplain API instead of Anymodel
    return await chat_completions_aixplain(request)



async def chat_completions_aixplain(request: ChatCompletionRequest):
    """Create chat completion using aiXplain backend"""

    if not request.messages:
        raise HTTPException(status_code=400, detail="No messages provided in the request.")

    # Extract current prompt from last message
    current_prompt_content = request.messages[-1].content

    # Build context and history for aiXplain
    context = ""
    history = []

    # Process previous messages for history
    processed_messages = request.messages[:-1]
    for i in range(0, len(processed_messages) - 1, 2):
        if i + 1 < len(processed_messages):
            user_msg = processed_messages[i]
            assistant_msg = processed_messages[i + 1]
            if user_msg.role == "user" and assistant_msg.role == "assistant":
                history.append({
                    "user": user_msg.content,
                    "assistant": assistant_msg.content
                })

    # Set parameters with defaults
    max_tokens = request.max_tokens or 64000
    temperature = request.temperature if request.temperature is not None else 0.0
    top_p = request.top_p if request.top_p is not None else 1.0

    try:
        # Call aiXplain API
        result = await call_aixplain_api(
            model_id=request.model,
            text=current_prompt_content,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            context=context,
            history=history,
            timeout=300
        )

        # Extract content and usage from aiXplain response
        content = result.get("data", "")
        usage_info = result.get("usage", {})

        # Convert usage to OpenAI format
        usage = {
            "prompt_tokens": int(usage_info.get("prompt_tokens", 0)),
            "completion_tokens": int(usage_info.get("completion_tokens", 0)),
            "total_tokens": int(usage_info.get("total_tokens", 0))
        }

        # Handle streaming vs non-streaming response
        if request.stream:
            # Fake streaming response for aiXplain
            async def stream_response_generator():
                stream_id = f"chatcmpl-{uuid.uuid4().hex}"
                created_time = int(time.time())

                # Send role delta
                yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={'role': 'assistant'})]).model_dump_json()}\n\n"

                # Send content delta
                if content:
                     yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={'content': content})]).model_dump_json()}\n\n"

                # Send finish reason
                yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={}, finish_reason='stop')]).model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"

            return StreamingResponse(stream_response_generator(), media_type="text/event-stream")
        else:
            # Non-streaming response
            return ChatCompletionResponse(
                model=request.model,
                choices=[ChatCompletionChoice(message=ChatMessage(role="assistant", content=content))],
                usage=usage
            )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        error_detail = f"Internal server error during aiXplain call: {str(e)}"
        import traceback
        print(traceback.format_exc())
        if request.stream:
            async def error_stream_gen_generic():
                yield f'data: {json.dumps({"error": {"message": error_detail, "type": "internal_server_error"}})}\n\n'
                yield "data: [DONE]\n\n"
            return StreamingResponse(error_stream_gen_generic(), media_type="text/event-stream", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=error_detail)

if __name__ == "__main__":
    import uvicorn
    
    # Check for required files and create dummy ones if missing
    if not os.path.exists("models.json"):
        print("CRITICAL: models.json not found. This service requires models.json with the specified aiXplain structure.")

    if not os.path.exists("client_api_keys.json"):
         print("Warning: client_api_keys.json not found. Creating a dummy file.")
         dummy_client_key = f"sk-dummy-{uuid.uuid4().hex}"
         with open("client_api_keys.json", "w", encoding="utf-8") as f:
             json.dump([dummy_client_key], f, indent=2)
         print(f"Created dummy client_api_keys.json with key: {dummy_client_key}. Clients should use this key.")

    # Load configurations for startup info
    load_models()
    load_client_api_keys()

    print("\n--- aiXplain OpenAI API Adapter ---")
    print("Endpoints:")
    print("  GET  /v1/models (Client API Key Auth)")
    print("  GET  /models (No Auth - for compatibility)")
    print("  POST /v1/chat/completions (aiXplain backend - Client API Key Auth)")

    client_keys_preview = list(VALID_CLIENT_KEYS)
    print(f"\nClient API Key(s) for this proxy: {client_keys_preview if client_keys_preview else 'No client API keys loaded. Check client_api_keys.json.'}")

    loaded_models_count = len(models_data.get("data", []))
    print(f"Loaded base-tier text models: {loaded_models_count}")
    if loaded_models_count == 0:
        print("Ensure 'models.json' is present, correctly formatted, and contains 'base' tier text models.")
    
    print("------------------------------------")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)