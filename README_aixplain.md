# aiXplain OpenAI API Adapter

这个项目现在支持 aiXplain API，提供与 OpenAI API 兼容的接口。

## 新功能

### aiXplain API 集成

- **主要端点**: `POST /v1/chat/completions` 使用 aiXplain 后端

### API 流程

1. **执行请求**: 发送 POST 请求到 `/api/v1/execute/{model_id}`
2. **轮询结果**: 自动轮询返回的 data URL 直到获取完整结果
3. **格式转换**: 将 aiXplain 响应转换为 OpenAI 格式

## 配置

### aiXplain API 配置

在 `run_token.py` 中配置：

```python
AIXPLAIN_API_BASE_URL = "https://platform.aixplain.com"
AIXPLAIN_API_KEY = "your-api-key-here"
```

### 模型配置

使用 `models_example.json` 作为模板创建 `models.json`：

```json
{
  "textModelList": [
    {
      "name": "aiXplain",
      "models": [
        {
          "name": "your-model-id",
          "tier": "base",
          "unavailable": false
        }
      ]
    }
  ]
}
```

## 使用方法

### 启动服务器

```bash
python run_token.py
```

### 发送请求

```python
import httpx

# 使用 aiXplain 后端
response = httpx.post(
    "http://localhost:8000/v1/chat/completions",
    headers={"Authorization": "Bearer your-client-api-key"},
    json={
        "model": "your-model-id",
        "messages": [{"role": "user", "content": "你是谁？"}],
        "temperature": 0.0,
        "max_tokens": 64000
    }
)
```

## 测试

运行测试脚本：

```bash
python test_aixplain_api.py
```

## 支持的参数

### aiXplain API 参数

- `text`: 用户输入文本
- `max_tokens`: 最大令牌数（默认：64000）
- `temperature`: 温度参数（默认：0.0）
- `top_p`: Top-p 参数（默认：1.0）
- `context`: 上下文信息
- `history`: 对话历史
- `timeout`: 超时时间（默认：300秒）

### OpenAI 兼容参数

- `model`: 模型 ID
- `messages`: 消息列表
- `stream`: 流式响应（支持）
- `temperature`: 温度参数
- `max_tokens`: 最大令牌数
- `top_p`: Top-p 参数

## 响应格式

### aiXplain 原始响应

```json
{
  "status": "SUCCESS",
  "completed": true,
  "data": "AI助手的回复内容",
  "runTime": 4.071,
  "usedCredits": 0.001752,
  "usage": {
    "prompt_tokens": "24",
    "completion_tokens": "112",
    "total_tokens": 136
  }
}
```

### OpenAI 格式响应

```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "your-model-id",
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "AI助手的回复内容"
      },
      "index": 0,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 24,
    "completion_tokens": 112,
    "total_tokens": 136
  }
}
```

## 错误处理

- **轮询超时**: 5分钟后超时
- **API 错误**: 自动重试和错误传播
- **格式转换**: 安全的响应解析

## 注意事项

1. 确保 aiXplain API 密钥有效
2. 模型 ID 必须在 `models.json` 中配置
3. 客户端 API 密钥需要在 `client_api_keys.json` 中配置
4. 轮询间隔为 5 秒，最多轮询 60 次（5分钟）
